<div class="w-full px-4 sm:px-6 lg:px-10 xl:px-20 py-6">
  <!-- Contenedor principal -->
  <div
    class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all"
  >
    <!-- Cabecera -->
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
    >
      <div>
        <h2 class="text-xl font-semibold text-blue-600">
          Estadísticas de Leads
        </h2>
        <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">
          Resumen de registros por sede, supervisor y vendedor
        </p>
      </div>
      <div
        class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
      >
        <button
          type="button"
          (click)="volverALeads()"
          class="flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          Volver a Leads
        </button>
      </div>
    </div>

    <!-- Componente de Gráficos por Sede -->
    <app-graficos-sede
      [onVerDetallesCliente]="openDetails.bind(this)"
    ></app-graficos-sede>

    <!-- Separador -->
    <div class="my-8 border-t border-gray-200 dark:border-gray-700"></div>

    <!-- Componente de Gráficos de Rendimiento de Leads -->
    <app-graficos-rendimiento-leads></app-graficos-rendimiento-leads>

    <!-- Separador -->
    <div class="my-8 border-t border-gray-200 dark:border-gray-700"></div>

    <!-- Componente de Estadísticas por Coordinador -->
    <div class="mt-8">
      <div class="mb-6">
        <h3 class="text-xl font-semibold text-purple-600 dark:text-purple-400">
          Estadísticas por Supervisor
        </h3>
      </div>
      <app-estadisticas-coordinador></app-estadisticas-coordinador>
    </div>
  </div>
</div>

<!-- Modal de Detalle -->
<div
  class="modal-detalle-overlay fixed inset-0 flex items-center justify-center p-4 bg-black bg-opacity-50 transition-all duration-300 ease-in-out"
  [class.hidden]="!modalVisible"
  (click)="closeModal()"
>
  <div
    class="w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900 rounded-lg shadow-xl overflow-auto animate-[zoomIn_0.3s_ease-in-out]"
    (click)="$event.stopPropagation()"
    role="dialog"
    aria-modal="true"
  >
    <div
      class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700"
    >
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
        {{ formatDateArrayWithTime((selectedCliente$ | async)?.fechaCreacion) }}
      </h2>
      <div class="flex items-center gap-2">
        <!-- Botón Excel -->
        <button
          class="p-2 rounded-full text-green-600 hover:bg-green-50 hover:text-green-700 dark:text-green-400 dark:hover:bg-gray-800 dark:hover:text-green-300 transition-colors"
          (click)="downloadModalExcel()"
          title="Descargar Excel"
          *ngIf="
            (user$ | async)?.role === 'ADMIN' ||
            (user$ | async)?.role === 'BACKOFFICE'
          "
        >
          <mat-icon>table_chart</mat-icon>
        </button>
        <!-- Botón PDF -->
        <button
          class="p-2 rounded-full text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-gray-800 dark:hover:text-red-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          (click)="downloadOrPrint('clienteDetalle')"
          title="Descargar PDF"
          [disabled]="exportLoading"
          *ngIf="
            (user$ | async)?.role === 'ADMIN' ||
            (user$ | async)?.role === 'BACKOFFICE'
          "
        >
          <mat-icon>picture_as_pdf</mat-icon>
        </button>
        <button
          class="p-2 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white transition-colors"
          (click)="closeModal()"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <!-- Contenido del modal con id para la impresión -->
    <div class="p-3 dark:bg-gray-900">
      <div id="clienteDetalle" class="space-y-1 dark:text-gray-200">
        <ng-container
          *ngIf="
            selectedCliente$ | async as clienteDetalle;
            else loadingOrError
          "
        >
          <form [formGroup]="editForm">
            <!-- Sección 1: DATOS DEL CLIENTE -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              DATOS DEL CLIENTE
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div
                class="flex border-b border-gray-200"
                *ngIf="clienteDetalle.id"
              >
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ID:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.id }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NOMBRES Y APELLIDOS:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.nombresApellidos || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NIF/NIE:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.nifNie || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NACIONALIDAD:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.nacionalidad || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FECHA DE NACIMIENTO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.fechaNacimiento || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FECHA DE CREACIÓN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ formatDateArrayWithTime(clienteDetalle.fechaCreacion) }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CORREO ELECTRÓNICO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.correoElectronico || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  MÓVIL DE CONTACTO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.movilContacto || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FIJO COMPAÑÍA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.fijoCompania || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  DIRECCIÓN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.direccion || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CÓDIGO POSTAL:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.codigoPostal || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  PROVINCIA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.provincia || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  DISTRITO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.distrito || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CIUDAD:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.ciudad || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NÚMERO DE AGENTE:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.numeroAgente || "No especificado" }}
                </div>
              </div>
              <div
                class="flex border-b border-gray-200"
                *ngIf="clienteDetalle.usuario"
              >
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ASESOR:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.usuario.nombre }}
                  {{ clienteDetalle.usuario.apellido }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  SUPERVISOR:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container
                    *ngIf="
                      clienteDetalle.usuario &&
                        clienteDetalle.usuario.coordinador;
                      else noCoordinador
                    "
                  >
                    {{ clienteDetalle.usuario.coordinador.nombre }}
                    {{ clienteDetalle.usuario.coordinador.apellido }}
                  </ng-container>
                  <ng-template #noCoordinador> No asignado </ng-template>
                </div>
              </div>
            </div>

            <!-- Sección 2: DATOS DE LA PROMOCIÓN -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              DATOS DE LA PROMOCIÓN
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CAMPAÑA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.campania || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NÚMEROS MÓVILES:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.numeroMoviles || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  PLAN ACTUAL:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.planActual || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TIPO DE PLAN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.tipoPlan || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TECNOLOGÍA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.tipoTecnologia || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TIPO DE FIBRA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.tipoFibra || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TIPO DE VELOCIDAD:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.velocidad || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TITULAR DEL SERVICIO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.titularDelServicio ? "Sí" : "No" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FÚTBOL:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.futbol || "- No especificado" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ICC:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.icc || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CUENTA BANCARIA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.cuentaBancaria || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  PERMANENCIA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.permanencia || "-" }}
                </div>
              </div>
            </div>

            <!-- Sección 3: INFORMACIÓN ADICIONAL -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              INFORMACIÓN ADICIONAL
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  OBSERVACIÓN ESTADO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.observacionEstado || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NÚMERO DE MÓVILES:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.numeroMoviles || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  MÓVILES A PORTAR:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <div
                    *ngIf="
                      clienteDetalle.movilesAPortar?.length;
                      else noMoviles
                    "
                  >
                    <div
                      *ngFor="let movil of clienteDetalle.movilesAPortar"
                      class="mb-1 last:mb-0"
                    >
                      {{ movil }}
                    </div>
                  </div>
                  <ng-template #noMoviles>-</ng-template>
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  OBSERVACIÓN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.observacion || "-" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ESTADO DE LLAMADA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.estadoLlamada || "-" }}
                </div>
              </div>
            </div>

            <!-- Sección 4: AUTORIZACIONES Y CONFIRMACIONES -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              AUTORIZACIONES Y CONFIRMACIONES
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  DESEA PROMOCIONES LOWI:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <span class="text-red-600 font-bold">X</span>
                  {{ clienteDetalle.deseaPromocionesLowi ? "Sí" : "No" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  AUTORIZA SEGUROS:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <span class="text-red-600 font-bold">X</span>
                  {{ clienteDetalle.autorizaSeguros ? "Sí" : "No" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  AUTORIZA ENERGÍAS:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <span class="text-red-600 font-bold">X</span>
                  {{ clienteDetalle.autorizaEnergias ? "Sí" : "No" }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  VENTA REALIZADA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <span class="text-red-600 font-bold">X</span>
                  {{ clienteDetalle.ventaRealizada ? "Sí" : "No" }}
                </div>
              </div>
            </div>
          </form>
        </ng-container>

        <!-- Estado de carga o error -->
        <ng-template #loadingOrError>
          <ng-container *ngIf="loading$ | async; else errorTemplate">
            <div
              class="flex flex-col items-center justify-center p-12 bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
            >
              <div class="relative">
                <div
                  class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700"
                ></div>
                <div
                  class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"
                ></div>
              </div>
              <div class="mt-4 text-center">
                <p class="text-gray-600 dark:text-gray-400 font-medium">
                  Cargando detalles del cliente...
                </p>
              </div>
            </div>
          </ng-container>
          <ng-template #errorTemplate>
            <ng-container *ngIf="error$ | async as errorMsg">
              <div
                class="flex flex-col items-center justify-center p-12 bg-red-50 dark:bg-red-900/20 border border-dashed border-red-300 dark:border-red-800 rounded-lg"
              >
                <div
                  class="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mb-4"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div class="text-center">
                  <p class="text-red-600 dark:text-red-400 font-medium mb-2">
                    Error al cargar los detalles del cliente
                  </p>
                  <p class="text-red-500 dark:text-red-300 text-sm">
                    {{ errorMsg }}
                  </p>
                </div>
              </div>
            </ng-container>
          </ng-template>
        </ng-template>
      </div>
    </div>

    <!-- Acciones del modal -->
    <div
      class="flex justify-end items-center p-4 border-t border-gray-200 dark:border-gray-700"
    >
      <button
        type="button"
        (click)="closeModal()"
        class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-md font-medium transition"
      >
        Cerrar
      </button>
    </div>
  </div>
</div>
