<!-- C<PERSON><PERSON><PERSON> del diálogo -->
<div
  mat-dialog-title
  class="flex items-center justify-between border-b pb-4 mb-4"
>
  <div>
    <h2 class="text-xl font-semibold text-blue-600">Leads del Asesor</h2>
    <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">
      Leads de {{ data.nombreAsesor }} para
      <span *ngIf="!data.fechaFin || data.fechaFin === data.fecha"
        >el {{ data.fecha }}</span
      >
      <span *ngIf="data.fechaFin && data.fechaFin !== data.fecha"
        >el rango {{ data.fecha }} - {{ data.fechaFin }}</span
      >
    </p>
  </div>
  <button
    mat-icon-button
    (click)="cerrarDialog()"
    class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
  >
    <mat-icon>close</mat-icon>
  </button>
</div>

<!-- Contenido del diálogo -->
<div mat-dialog-content class="max-h-[70vh] overflow-auto">
  <!-- <PERSON><PERSON> de b<PERSON>da -->
  <div
    class="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
  >
    <div class="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
      <div class="flex-1">
        <label
          for="buscarMovil"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Buscar por Número Móvil
        </label>
        <div class="relative">
          <input
            id="buscarMovil"
            type="text"
            [formControl]="busquedaMovilControl"
            (keyup.enter)="buscarPorMovil()"
            placeholder="Ingrese número móvil... (búsqueda automática)"
            class="w-full px-4 py-2 pl-10 pr-10 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
          />
          <div
            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
          >
            <svg
              class="h-4 w-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
          <!-- Indicador de búsqueda en tiempo real -->
          <div
            *ngIf="loading"
            class="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <svg
              class="animate-spin h-4 w-4 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        </div>
      </div>
      <div class="flex gap-2 mt-6 sm:mt-0">
        <button
          (click)="buscarPorMovil()"
          [disabled]="loading"
          class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <svg
            class="w-4 h-4 mr-2 inline"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            ></path>
          </svg>
          Buscar
        </button>
        <button
          (click)="limpiarBusqueda()"
          [disabled]="loading"
          class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <svg
            class="w-4 h-4 mr-2 inline"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
          Limpiar
        </button>
      </div>
    </div>

    <!-- Indicador de búsqueda activa -->
    <div
      *ngIf="busquedaMovilControl.value && busquedaMovilControl.value.trim()"
      class="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
    >
      <p class="text-sm text-blue-700 dark:text-blue-300">
        <svg
          class="w-4 h-4 inline mr-1"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
        Filtrando por número móvil:
        <strong>{{ busquedaMovilControl.value }}</strong>
        <span class="ml-2 text-xs opacity-75">(búsqueda en tiempo real)</span>
      </p>
    </div>
  </div>

  <!-- Indicador de carga -->
  <div
    *ngIf="loading"
    class="flex flex-col items-center justify-center p-12 bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
  >
    <div class="relative">
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700"
      ></div>
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"
      ></div>
    </div>
    <div class="mt-4 text-center">
      <p class="text-gray-600 dark:text-gray-400 font-medium">
        Cargando leads...
      </p>
    </div>
  </div>

  <!-- Tabla de leads -->
  <div
    *ngIf="!loading && leads && leads.length > 0"
    class="overflow-x-auto rounded-xl shadow mt-4"
  >
    <table
      class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
    >
      <thead>
        <tr>
          <th
            class="px-6 py-3 bg-blue-500 dark:bg-blue-700 text-white uppercase text-xs font-semibold"
          >
            DNI
          </th>
          <th
            class="px-6 py-3 bg-blue-500 dark:bg-blue-700 text-white uppercase text-xs font-semibold"
          >
            Asesor
          </th>
          <th
            class="px-6 py-3 bg-blue-500 dark:bg-blue-700 text-white uppercase text-xs font-semibold"
          >
            Fecha Ingresado
          </th>
          <th
            class="px-6 py-3 bg-blue-500 dark:bg-blue-700 text-white uppercase text-xs font-semibold"
          >
            Número Móvil
          </th>
          <th
            class="px-6 py-3 bg-blue-500 dark:bg-blue-700 text-white uppercase text-xs font-semibold"
          >
            Supervisor
          </th>
          <th
            class="px-6 py-3 bg-blue-500 dark:bg-blue-700 text-white uppercase text-xs font-semibold text-center"
          >
            Acciones
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let lead of leads"
          class="hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 font-medium"
          >
            {{ lead.dni }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ lead.asesor }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ lead.getFechaCreacionFormatted() || lead.fechaIngresado }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ lead.numeroMovil }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ lead.coordinador || "-" }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center"
          >
            <div class="flex justify-center space-x-2">
              <!-- Botón Ver Detalles -->
              <button
                (click)="verDetalles(lead)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 transition-colors duration-200"
                title="Ver detalles del cliente"
              >
                <svg
                  class="w-3 h-3 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  ></path>
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  ></path>
                </svg>
                Ver
              </button>

              <!-- Botón Descargar Excel -->
              <button
                (click)="descargarExcel(lead)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 hover:text-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/30 dark:hover:text-green-300 transition-colors duration-200"
                title="Descargar datos del cliente en Excel"
              >
                <svg
                  class="w-3 h-3 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  ></path>
                </svg>
                Excel
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Paginación inferior -->
  <mat-paginator
    #leadsPaginator
    *ngIf="!loading && leads && leads.length > 0"
    class="bg-white dark:bg-gray-900 border-t-0 border border-gray-200 dark:border-gray-700 mt-4"
    [length]="totalElements"
    [pageIndex]="currentPage"
    [pageSize]="pageSize"
    [pageSizeOptions]="pageSizeOptions"
    [showFirstLastButtons]="true"
    (page)="handlePageEvent($event)"
  >
  </mat-paginator>

  <!-- Estado vacío -->
  <div
    *ngIf="!loading && leads?.length === 0"
    class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg mt-4"
  >
    <div class="text-5xl text-gray-400 dark:text-gray-600 mb-4">👥</div>
    <p class="text-gray-600 dark:text-gray-400">
      No hay leads disponibles para el asesor {{ data.nombreAsesor }} en
      <span *ngIf="!data.fechaFin || data.fechaFin === data.fecha"
        >la fecha {{ data.fecha }}</span
      >
      <span *ngIf="data.fechaFin && data.fechaFin !== data.fecha"
        >el rango {{ data.fecha }} - {{ data.fechaFin }}</span
      >
    </p>
  </div>
</div>

<!-- Acciones del diálogo -->
<div mat-dialog-actions class="flex justify-end pt-4 border-t">
  <button
    mat-button
    (click)="cerrarDialog()"
    class="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
  >
    Cerrar
  </button>
</div>
